"""
Department-specific data and policies for Meril Group Companies
Based on official HR documents and organizational structure
"""

# Department-specific information and responsibilities
DEPARTMENT_INFO = {
    "hr": {
        "full_name": "Human Resources",
        "responsibilities": [
            "Employee recruitment and onboarding",
            "Leave policy administration",
            "Performance management",
            "Employee relations and grievances",
            "Training and development",
            "Compensation and benefits",
            "Policy implementation and compliance"
        ],
        "key_contacts": [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>"
        ],
        "specific_policies": {
            "leave_approval": "HR Department approval required for all leave applications",
            "grievance_handling": "First point of contact for employee grievances",
            "policy_queries": "All policy clarifications to be directed to HR"
        }
    },
    "it": {
        "full_name": "Information Technology",
        "responsibilities": [
            "IT infrastructure management",
            "Software and hardware support",
            "Data security and backup",
            "Network administration",
            "Digital transformation initiatives"
        ],
        "specific_policies": {
            "equipment_usage": "Company IT equipment for business use only",
            "data_security": "Strict adherence to data protection protocols",
            "software_licensing": "Only authorized software to be used"
        }
    },
    "finance": {
        "full_name": "Finance and Accounts",
        "responsibilities": [
            "Financial planning and analysis",
            "Expense reimbursement processing",
            "Budget management",
            "Audit and compliance",
            "Tax planning and filing"
        ],
        "specific_policies": {
            "expense_claims": "All expense claims must be submitted with original bills",
            "reimbursement_timeline": "Expense statements within 10 days of tour completion",
            "approval_hierarchy": "HOD approval required before finance processing"
        }
    },
    "sales": {
        "full_name": "Sales and Marketing",
        "responsibilities": [
            "Customer relationship management",
            "Sales target achievement",
            "Market analysis and strategy",
            "Product promotion and marketing",
            "Client servicing"
        ],
        "specific_policies": {
            "travel_frequency": "Higher travel allowances due to field work",
            "client_entertainment": "Alcohol bills allowed only for official guest entertainment",
            "mobile_reimbursement": "Enhanced mobile phone reimbursement for sales team"
        }
    },
    "operations": {
        "full_name": "Operations and Manufacturing",
        "responsibilities": [
            "Production planning and control",
            "Quality assurance",
            "Supply chain management",
            "Inventory management",
            "Process optimization"
        ],
        "specific_policies": {
            "safety_protocols": "Strict adherence to health and safety norms",
            "shift_management": "Special leave policies for shift workers",
            "equipment_handling": "Proper training required for equipment operation"
        }
    },
    "quality_assurance": {
        "full_name": "Quality Assurance",
        "responsibilities": [
            "Product quality testing",
            "Compliance with regulatory standards",
            "Quality system maintenance",
            "Documentation and reporting",
            "Continuous improvement initiatives"
        ],
        "specific_policies": {
            "documentation": "Detailed documentation required for all processes",
            "compliance": "Strict adherence to regulatory guidelines",
            "training": "Regular training on quality standards"
        }
    },
    "research_development": {
        "full_name": "Research and Development",
        "responsibilities": [
            "Product innovation and development",
            "Research project management",
            "Patent and IP management",
            "Technology evaluation",
            "Clinical trials coordination"
        ],
        "specific_policies": {
            "confidentiality": "Enhanced confidentiality requirements for R&D projects",
            "ip_protection": "Strict intellectual property protection protocols",
            "collaboration": "External collaboration agreements require approval"
        }
    }
}

# Department mapping for easy lookup
DEPARTMENT_MAPPING = {
    "hr": "hr",
    "human resources": "hr",
    "human resource": "hr",
    
    "it": "it",
    "information technology": "it",
    "tech": "it",
    "technology": "it",
    
    "finance": "finance",
    "accounts": "finance",
    "accounting": "finance",
    "finance and accounts": "finance",
    
    "sales": "sales",
    "marketing": "sales",
    "sales and marketing": "sales",
    "business development": "sales",
    
    "operations": "operations",
    "manufacturing": "operations",
    "production": "operations",
    "ops": "operations",
    
    "quality assurance": "quality_assurance",
    "qa": "quality_assurance",
    "quality": "quality_assurance",
    
    "research and development": "research_development",
    "r&d": "research_development",
    "rd": "research_development",
    "research": "research_development",
    "development": "research_development"
}

def get_department_key(department_name):
    """Get the standardized department key from department name"""
    if not department_name:
        return None
    
    dept_name = department_name.lower().strip()
    return DEPARTMENT_MAPPING.get(dept_name, None)

def get_department_info(department_name):
    """Get department-specific information"""
    dept_key = get_department_key(department_name)
    if dept_key and dept_key in DEPARTMENT_INFO:
        return DEPARTMENT_INFO[dept_key]
    return None

def get_department_specific_policies(department_name):
    """Get department-specific policies"""
    dept_info = get_department_info(department_name)
    if dept_info and "specific_policies" in dept_info:
        return dept_info["specific_policies"]
    return None
