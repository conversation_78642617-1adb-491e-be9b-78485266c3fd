#!/usr/bin/env python3
"""
Demo script for HR Chatbot
This script demonstrates the chatbot functionality without requiring a full RASA setup
"""


def get_leave_policy_response(organization=None, department=None, intent_name="ask_leave_policy"):
    """Simulate leave policy response with organization and department specificity"""

    leave_info = {
        "casual_leave": {
            "title": "🏖️ **Casual Leave Policy**",
            "details": [
                "• **Entitlement**: 7 days per year (pro-rata basis)",
                "• **Credit**: First day of calendar year",
                "• **Accumulation**: Cannot be carried forward beyond current year",
                "• **Probation**: Available on pro-rata basis for probationary employees",
                "• **Half Day**: Minimum 4 hours 15 minutes work required",
                "• **Application**: Must be filled within 2 days of availing leave"
            ]
        },
        "privilege_leave": {
            "title": "🌴 **Privilege Leave (PL) Policy**",
            "details": [
                "• **Entitlement**: 30 days per year (if 240 working days completed)",
                "• **Minimum Duration**: 3 days minimum per application",
                "• **Advance Notice**: 10 days advance application required",
                "• **Carry Forward**: Up to 60 days can be carried forward",
                "• **Encashment**: Excess over 60 days will be encashed",
                "• **Credit Date**: January 7th every year",
                "• **Emergency**: Short notice PL on emergency grounds (management discretion)",
                "• **Extension**: Application for extension must reach before expiry"
            ]
        },
        "sick_leave": {
            "title": "🏥 **Sick Leave Policy**",
            "details": [
                "• **Entitlement**: 7 days per year for confirmed employees",
                "• **Pro-rata**: Based on confirmation/resignation timing",
                "• **Medical Certificate**: Required for 3+ consecutive days",
                "• **Notification**: Inform company during leave period with medical certificate",
                "• **Extended Sickness**: Can use PL after SL exhaustion",
                "• **Half Day**: Minimum 4 hours 15 minutes work required"
            ]
        }
    }

    if intent_name == "ask_casual_leave":
        response = f"{leave_info['casual_leave']['title']}\n\n" + "\n".join(leave_info['casual_leave']['details'])
    elif intent_name == "ask_privilege_leave":
        response = f"{leave_info['privilege_leave']['title']}\n\n" + "\n".join(leave_info['privilege_leave']['details'])
    elif intent_name == "ask_sick_leave":
        response = f"{leave_info['sick_leave']['title']}\n\n" + "\n".join(leave_info['sick_leave']['details'])
    else:
        response = "📋 **Meril Group Leave Policies**\n\nI can help you with Casual Leave, Privilege Leave, Sick Leave, and Maternity Leave policies."

    # Add organization-specific information
    if organization:
        response += f"\n\n🏢 **Organization**: {organization}"

    # Add department-specific information
    if department:
        dept_mapping = {
            "hr": "Human Resources",
            "it": "Information Technology",
            "finance": "Finance and Accounts",
            "sales": "Sales and Marketing"
        }
        dept_full_name = dept_mapping.get(department.lower(), department)
        response += f"\n🏛️ **Department**: {dept_full_name}"

        # Add department-specific notes
        if department.lower() == "hr":
            response += "\n📝 **Department Note**: HR Department approval required for all leave applications"
            response += "\n💼 **HR Department**: You can directly process leave applications and queries"
        elif department.lower() == "sales":
            response += "\n📝 **Department Note**: Enhanced travel allowances due to field work requirements"
        elif department.lower() == "finance":
            response += "\n📝 **Department Note**: All expense claims must be submitted with original bills"

    # Add general guidelines
    response += "\n\n**📋 General Guidelines:**"
    response += "\n• All leaves (except sick leave) must be applied in advance"
    response += "\n• Leave applications require HOD approval"
    response += "\n• Weekly/Paid holidays during PL are counted as leave days"
    response += "\n• Authority to approve leaves rests with Department Head and HR Department"

    return response


def get_sexual_harassment_policy_response(organization=None):
    """Simulate sexual harassment policy response with organization-specific committee info"""

    response = """🚫 **Sexual Harassment Prevention Policy**

**Definition of Sexual Harassment:**
Sexual harassment includes unwelcome sexually determined behavior such as:
• Physical contact and sexual advances
• Demand or request for sexual favors
• Sexually-colored remarks
• Showing pornography
• Any other unwelcome physical, verbal or non-verbal conduct of sexual nature"""

    # Organization-specific committee information
    committees = {
        "meril life sciences": {
            "name": "Meril Life Sciences Pvt. Ltd.",
            "members": [
                "**Presiding Officer**: Anita Nagar (<EMAIL>, **********)",
                "**Member**: Gayathri Nair, DGM (<EMAIL>, **********)",
                "**Member**: Ankita Patel, Manager (<EMAIL>, **********)",
                "**External Associate**: Neha Desai (<EMAIL>, **********)"
            ]
        },
        "meril healthcare": {
            "name": "Meril Healthcare Pvt. Ltd.",
            "members": [
                "**Presiding Officer**: Pallabi Sarkar, DGM (<EMAIL>, **********)",
                "**Member**: Anushree Uniyal, Manager (<EMAIL>, **********)",
                "**Member**: Punita Patel, Assistant Manager (<EMAIL>, **********)",
                "**External Member**: Neha Desai (<EMAIL>, **********)"
            ]
        },
        "meril diagnostics": {
            "name": "Meril Diagnostics Pvt. Ltd.",
            "members": [
                "**Presiding Officer**: Twisha Hathi, DGM (<EMAIL>, **********)",
                "**Member**: Chintal N Patel, Asst. Manager (<EMAIL>, **********)",
                "**Member**: Tejalkumari A. Patel, Manager (<EMAIL>, **********)",
                "**External Member**: Neha Desai (<EMAIL>, **********)"
            ]
        },
        "meril endo surgery": {
            "name": "Meril Endo Surgery Pvt. Ltd.",
            "members": [
                "**Presiding Officer**: Ami Rughani, DGM (<EMAIL>, **********)",
                "**Member**: Hemaxi Patel, Asst. Manager (<EMAIL>, 9727650310)",
                "**Member**: Ankita Rana, Assit. Manager (<EMAIL>, 9978021178)",
                "**External Member**: Neha Desai (<EMAIL>, **********)"
            ]
        }
    }

    if organization:
        org_key = organization.lower().replace("pvt. ltd.", "").replace("pvt ltd", "").strip()
        if org_key in committees:
            committee = committees[org_key]
            response += f"\n\n**🏢 {committee['name']} - Committee Members:**"
            for member in committee['members']:
                response += f"\n• {member}"
            response += f"\n\n*Committee validity: 17th Jan 2022 to 16th Jan 2025*"
        else:
            response += f"\n\n🏢 **Organization**: {organization}"
            response += "\n\n*Please contact HR for specific committee information*"
    else:
        response += "\n\n**Committee Members by Organization:**"
        for org_key, committee in committees.items():
            response += f"\n\n**🏢 {committee['name']}:**"
            response += f"\n• {committee['members'][0]}"  # Show presiding officer
        response += "\n\n*All committees valid: 17th Jan 2022 to 16th Jan 2025*"

    response += "\n\n**📋 Redressal Process:**"
    response += "\n• Send complaint in writing in sealed envelope to Head of Human Resource"
    response += "\n• Investigation will be conducted and report submitted to Managing Director"
    response += "\n• Counseling and appropriate measures will be taken"
    response += "\n• Disciplinary action if grievance persists"
    response += "\n\n⚠️ **Important**: All complaints are handled with strict confidentiality and zero tolerance for retaliation"

    return response


def get_contact_info_response():
    """Simulate contact info response"""
    return """📞 **HR Contact Information**

**General HR Queries:**
• **Email**: <EMAIL>
• **Address**: Bilakhia House, Survey No.135/139, Muktanand Marg, Chala, Vapi-396191, Gujarat, India
• **Phone**: +91 ***********
• **Website**: www.merillife.com

**Sexual Harassment Committee Contacts:**
• **Meril Life Sciences**: Anita Nagar (<EMAIL>, **********)
• **Meril Healthcare**: Pallabi Sarkar (<EMAIL>, **********)
• **Meril Diagnostics**: Twisha Hathi (<EMAIL>, **********)
• **Meril Endo Surgery**: Ami Rughani (<EMAIL>, **********)"""


def demo_chatbot():
    print("🤖 HR Chatbot Demo for Meril Group Companies")
    print("🎯 Organization & Department-Specific Responses")
    print("=" * 60)

    # Demo scenarios showcasing organization and department specificity
    scenarios = [
        {
            "title": "Casual Leave Policy - HR Department",
            "organization": "Meril Life Sciences Pvt. Ltd.",
            "department": "HR",
            "intent": "ask_casual_leave",
            "function": get_leave_policy_response
        },
        {
            "title": "Privilege Leave Policy - Sales Department",
            "organization": "Meril Healthcare Pvt. Ltd.",
            "department": "Sales",
            "intent": "ask_privilege_leave",
            "function": get_leave_policy_response
        },
        {
            "title": "Sick Leave Policy - IT Department",
            "organization": "Meril Diagnostics Pvt. Ltd.",
            "department": "IT",
            "intent": "ask_sick_leave",
            "function": get_leave_policy_response
        },
        {
            "title": "Sexual Harassment Policy - Meril Life Sciences",
            "organization": "Meril Life Sciences Pvt. Ltd.",
            "department": None,
            "intent": "ask_sexual_harassment",
            "function": get_sexual_harassment_policy_response
        },
        {
            "title": "Sexual Harassment Policy - Meril Healthcare",
            "organization": "Meril Healthcare Pvt. Ltd.",
            "department": None,
            "intent": "ask_sexual_harassment",
            "function": get_sexual_harassment_policy_response
        },
        {
            "title": "General Contact Information",
            "organization": None,
            "department": None,
            "intent": "ask_contact_info",
            "function": get_contact_info_response
        }
    ]

    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['title']}")
        if scenario['organization']:
            print(f"   🏢 Organization: {scenario['organization']}")
        if scenario['department']:
            print(f"   🏛️ Department: {scenario['department']}")
        print("-" * 50)

        # Get response based on scenario
        if scenario['function'] == get_contact_info_response:
            response = scenario['function']()
        elif scenario['function'] == get_sexual_harassment_policy_response:
            response = scenario['function'](scenario['organization'])
        else:
            response = scenario['function'](scenario['organization'], scenario['department'], scenario['intent'])

        print(response)
        print("\n" + "="*60)

    print("\n🎯 **Interactive Demo**")
    print("Experience organization & department-specific responses!")
    print("\nAvailable Organizations:")
    print("- Meril Life Sciences Pvt. Ltd.")
    print("- Meril Healthcare Pvt. Ltd.")
    print("- Meril Diagnostics Pvt. Ltd.")
    print("- Meril Endo Surgery Pvt. Ltd.")
    print("\nAvailable Departments:")
    print("- HR, IT, Finance, Sales, Operations, R&D")
    print("\nTry asking questions like:")
    print("- 'casual leave for HR at Meril Life Sciences'")
    print("- 'privilege leave for Sales at Meril Healthcare'")
    print("- 'sexual harassment policy for Meril Diagnostics'")
    print("- 'contact information'")
    print("\nType 'quit' to exit")

    # Store user context
    user_org = None
    user_dept = None

    while True:
        user_input = input("\n👤 You: ").strip()

        if user_input.lower() in ['quit', 'exit', 'bye']:
            print("🤖 Bot: Goodbye! Have a great day!")
            break

        # Parse organization from input
        org_detected = None
        if 'meril life sciences' in user_input.lower():
            org_detected = "Meril Life Sciences Pvt. Ltd."
        elif 'meril healthcare' in user_input.lower():
            org_detected = "Meril Healthcare Pvt. Ltd."
        elif 'meril diagnostics' in user_input.lower():
            org_detected = "Meril Diagnostics Pvt. Ltd."
        elif 'meril endo surgery' in user_input.lower():
            org_detected = "Meril Endo Surgery Pvt. Ltd."

        # Parse department from input
        dept_detected = None
        if ' hr ' in user_input.lower() or user_input.lower().endswith(' hr'):
            dept_detected = "HR"
        elif ' it ' in user_input.lower() or user_input.lower().endswith(' it'):
            dept_detected = "IT"
        elif 'finance' in user_input.lower():
            dept_detected = "Finance"
        elif 'sales' in user_input.lower():
            dept_detected = "Sales"
        elif 'operations' in user_input.lower():
            dept_detected = "Operations"

        # Update user context
        if org_detected:
            user_org = org_detected
        if dept_detected:
            user_dept = dept_detected

        print("🤖 Bot:")

        # Handle different query types
        if 'casual leave' in user_input.lower():
            response = get_leave_policy_response(user_org, user_dept, "ask_casual_leave")
            print(response)
        elif 'privilege leave' in user_input.lower() or ' pl ' in user_input.lower():
            response = get_leave_policy_response(user_org, user_dept, "ask_privilege_leave")
            print(response)
        elif 'sick leave' in user_input.lower():
            response = get_leave_policy_response(user_org, user_dept, "ask_sick_leave")
            print(response)
        elif 'sexual harassment' in user_input.lower() or 'harassment' in user_input.lower():
            response = get_sexual_harassment_policy_response(user_org)
            print(response)
        elif 'contact' in user_input.lower():
            response = get_contact_info_response()
            print(response)
        elif 'organization' in user_input.lower() or 'company' in user_input.lower():
            if user_org:
                print(f"You are currently set to: {user_org}")
            else:
                print("Please specify your organization (Meril Life Sciences, Meril Healthcare, Meril Diagnostics, or Meril Endo Surgery)")
        elif 'department' in user_input.lower():
            if user_dept:
                print(f"You are currently set to: {user_dept} department")
            else:
                print("Please specify your department (HR, IT, Finance, Sales, Operations, R&D)")
        else:
            print("I can help you with:")
            print("• Leave policies (casual, privilege, sick)")
            print("• Sexual harassment prevention policy")
            print("• Contact information")
            print("\nPlease specify your organization and department for personalized responses!")
            if user_org:
                print(f"Current Organization: {user_org}")
            if user_dept:
                print(f"Current Department: {user_dept}")


if __name__ == "__main__":
    demo_chatbot()
