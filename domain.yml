version: "3.1"

intents:
  - greet
  - goodbye
  - affirm
  - deny
  - provide_organization
  - provide_department
  - ask_leave_policy
  - ask_casual_leave
  - ask_privilege_leave
  - ask_sick_leave
  - ask_maternity_leave
  - ask_travel_policy
  - ask_lta_policy
  - ask_insurance_policy
  - ask_expense_limits
  - ask_sexual_harassment
  - ask_ethical_conduct
  - ask_general_hr_info
  - ask_contact_info
  - restart

entities:
  - organization
  - department
  - leave_type
  - policy_type
  - hr_topic

slots:
  organization:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: organization
  
  department:
    type: text
    influence_conversation: true
    mappings:
    - type: from_entity
      entity: department
  
  leave_type:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: leave_type
  
  policy_type:
    type: text
    influence_conversation: false
    mappings:
    - type: from_entity
      entity: policy_type

responses:
  utter_greet:
  - text: "Hello! I'm your HR Assistant for Meril Group Companies. I can help you with HR policies, leave information, travel policies, and more. Which organization are you from?"

  utter_ask_organization:
  - text: "Which Meril organization are you from? We have:\n- Meril Life Sciences Pvt. Ltd.\n- Meril Healthcare Pvt. Ltd.\n- Meril Diagnostics Pvt. Ltd.\n- Meril Endo Surgery Pvt. Ltd."

  utter_ask_department:
  - text: "Which department are you in? (e.g., HR, IT, Finance, Sales, Marketing, Operations, etc.)"

  utter_goodbye:
  - text: "Goodbye! Feel free to ask me anytime about HR policies and procedures. Have a great day!"

  utter_default:
  - text: "I'm sorry, I didn't understand that. I can help you with HR policies, leave information, travel policies, insurance, and other HR-related queries for Meril Group Companies."

  utter_ask_rephrase:
  - text: "I'm sorry, I didn't understand. Could you please rephrase your question about HR policies or procedures?"

actions:
  - action_get_leave_policy
  - action_get_travel_policy
  - action_get_insurance_info
  - action_get_expense_limits
  - action_get_harassment_policy
  - action_get_ethical_conduct
  - action_provide_contact_info
  - action_restart

session_config:
  session_expiration_time: 60
  carry_over_slots_to_new_session: true
