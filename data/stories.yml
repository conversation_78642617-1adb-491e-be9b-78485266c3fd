version: "3.1"

stories:

- story: greet and ask for organization
  steps:
  - intent: greet
  - action: utter_greet
  - intent: provide_organization
  - slot_was_set:
    - organization: "Meril Life Sciences"
  - action: utter_ask_department

- story: provide department and ask leave policy
  steps:
  - intent: provide_department
  - slot_was_set:
    - department: "HR"
  - intent: ask_leave_policy
  - action: action_get_leave_policy

- story: ask casual leave directly
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_casual_leave
  - action: action_get_leave_policy

- story: ask privilege leave directly
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_privilege_leave
  - action: action_get_leave_policy

- story: ask sick leave directly
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_sick_leave
  - action: action_get_leave_policy

- story: ask maternity leave directly
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_maternity_leave
  - action: action_get_leave_policy

- story: ask travel policy
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_travel_policy
  - action: action_get_travel_policy

- story: ask LTA policy
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_lta_policy
  - action: action_get_travel_policy

- story: ask insurance policy
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_insurance_policy
  - action: action_get_insurance_info

- story: ask expense limits
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_expense_limits
  - action: action_get_expense_limits

- story: ask sexual harassment policy
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_sexual_harassment
  - action: action_get_harassment_policy

- story: ask ethical conduct
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_ethical_conduct
  - action: action_get_ethical_conduct

- story: ask contact info
  steps:
  - intent: greet
  - action: utter_greet
  - intent: ask_contact_info
  - action: action_provide_contact_info

- story: say goodbye
  steps:
  - intent: goodbye
  - action: utter_goodbye

- story: restart conversation
  steps:
  - intent: restart
  - action: action_restart

- story: complete flow with organization and department
  steps:
  - intent: greet
  - action: utter_greet
  - intent: provide_organization
  - slot_was_set:
    - organization: "Meril Life Sciences"
  - action: utter_ask_department
  - intent: provide_department
  - slot_was_set:
    - department: "IT"
  - intent: ask_leave_policy
  - action: action_get_leave_policy
  - intent: goodbye
  - action: utter_goodbye
