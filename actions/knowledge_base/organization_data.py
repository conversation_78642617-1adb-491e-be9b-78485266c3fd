"""
Organization-specific data for Meril Group Companies
Based on official HR documents and policies
"""

# Organization-specific Sexual Harassment Committee Information
SEXUAL_HARASSMENT_COMMITTEES = {
    "meril_life_sciences": {
        "organization": "Meril Life Sciences Pvt. Ltd.",
        "committee": [
            {
                "role": "Presiding Officer",
                "name": "<PERSON> Nagar",
                "email": "<EMAIL>",
                "phone": "9714035588"
            },
            {
                "role": "Member",
                "name": "<PERSON><PERSON><PERSON>",
                "designation": "DGM",
                "email": "<EMAIL>",
                "phone": "9824173378"
            },
            {
                "role": "Member",
                "name": "<PERSON><PERSON><PERSON>",
                "designation": "Manager",
                "email": "<EMAIL>",
                "phone": "**********"
            },
            {
                "role": "External Associate",
                "name": "<PERSON><PERSON><PERSON>",
                "email": "<EMAIL>",
                "phone": "**********"
            }
        ],
        "validity": "17th Jan 2022 to 16th Jan 2025"
    },
    "meril_healthcare": {
        "organization": "Meril Healthcare Pvt. Ltd.",
        "committee": [
            {
                "role": "Presiding Officer",
                "name": "<PERSON><PERSON><PERSON> <PERSON>rkar",
                "designation": "DGM",
                "email": "<EMAIL>",
                "phone": "**********"
            },
            {
                "role": "Member",
                "name": "Anushree Uniyal",
                "designation": "Manager",
                "email": "<EMAIL>",
                "phone": "**********"
            },
            {
                "role": "Member",
                "name": "Punita Patel",
                "designation": "Assistant Manager",
                "email": "<EMAIL>",
                "phone": "**********"
            },
            {
                "role": "External Member",
                "name": "Neha Desai",
                "designation": "Associate NGO",
                "email": "<EMAIL>",
                "phone": "**********"
            }
        ],
        "validity": "17th Jan 2022 to 16th Jan 2025"
    },
    "meril_diagnostics": {
        "organization": "Meril Diagnostics Pvt. Ltd.",
        "committee": [
            {
                "role": "Presiding Officer",
                "name": "Twisha Hathi",
                "designation": "DGM",
                "email": "<EMAIL>",
                "phone": "9924146740"
            },
            {
                "role": "Member",
                "name": "Chintal N Patel",
                "designation": "Asst. Manager",
                "email": "<EMAIL>",
                "phone": "9619525902"
            },
            {
                "role": "Member",
                "name": "Tejalkumari A. Patel",
                "designation": "Manager",
                "email": "<EMAIL>",
                "phone": "9712154320"
            },
            {
                "role": "External Member",
                "name": "Neha Desai",
                "designation": "Associate NGO",
                "email": "<EMAIL>",
                "phone": "**********"
            }
        ],
        "validity": "17th Jan 2022 to 16th Jan 2025"
    },
    "meril_endo_surgery": {
        "organization": "Meril Endo Surgery Pvt. Ltd.",
        "committee": [
            {
                "role": "Presiding Officer",
                "name": "Ami Rughani",
                "designation": "DGM",
                "email": "<EMAIL>",
                "phone": "9601187409"
            },
            {
                "role": "Member",
                "name": "Hemaxi Patel",
                "designation": "Asst. Manager",
                "email": "<EMAIL>",
                "phone": "9727650310"
            },
            {
                "role": "Member",
                "name": "Ankita Rana",
                "designation": "Assit. Manager",
                "email": "<EMAIL>",
                "phone": "9978021178"
            },
            {
                "role": "External Member",
                "name": "Neha Desai",
                "designation": "Associate NGO",
                "email": "<EMAIL>",
                "phone": "**********"
            }
        ],
        "validity": "17th Jan 2022 to 16th Jan 2025"
    }
}

# Organization mapping for easy lookup
ORGANIZATION_MAPPING = {
    "meril life sciences": "meril_life_sciences",
    "meril life sciences pvt ltd": "meril_life_sciences",
    "meril life sciences pvt. ltd.": "meril_life_sciences",
    "meril life sciences india pvt ltd": "meril_life_sciences",
    "mls": "meril_life_sciences",
    
    "meril healthcare": "meril_healthcare",
    "meril healthcare pvt ltd": "meril_healthcare",
    "meril healthcare pvt. ltd.": "meril_healthcare",
    "mhc": "meril_healthcare",
    
    "meril diagnostics": "meril_diagnostics",
    "meril diagnostics pvt ltd": "meril_diagnostics",
    "meril diagnostics pvt. ltd.": "meril_diagnostics",
    "mdl": "meril_diagnostics",
    
    "meril endo surgery": "meril_endo_surgery",
    "meril endo surgery pvt ltd": "meril_endo_surgery",
    "meril endo surgery pvt. ltd.": "meril_endo_surgery",
    "meril endo-surgery": "meril_endo_surgery",
    "mes": "meril_endo_surgery"
}

# HR Contact Information
HR_CONTACTS = {
    "general": {
        "email": "<EMAIL>",
        "address": "Bilakhia House, Survey No.135/139, Muktanand Marg, Chala, Vapi-396191, Gujarat, India",
        "phone": "+91 ***********",
        "fax": "+91 ***********",
        "website": "www.merillife.com"
    },
    "emergency_contacts": [
        "Hetvi Desai",
        "Ami Rughani"
    ]
}

def get_organization_key(organization_name):
    """Get the standardized organization key from organization name"""
    if not organization_name:
        return None
    
    org_name = organization_name.lower().strip()
    return ORGANIZATION_MAPPING.get(org_name, None)

def get_sexual_harassment_committee(organization_name):
    """Get sexual harassment committee information for specific organization"""
    org_key = get_organization_key(organization_name)
    if org_key and org_key in SEXUAL_HARASSMENT_COMMITTEES:
        return SEXUAL_HARASSMENT_COMMITTEES[org_key]
    return None
