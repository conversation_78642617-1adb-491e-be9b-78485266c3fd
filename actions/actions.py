from typing import Any, Text, Dict, List
from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet, Restarted


class ActionGetLeavePolicy(Action):
    def name(self) -> Text:
        return "action_get_leave_policy"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        organization = tracker.get_slot("organization")
        intent = tracker.latest_message['intent'].get('name')

        # Base leave policy information from your HR documents
        leave_info = {
            "casual_leave": {
                "title": "🏖️ **Casual Leave Policy**",
                "details": [
                    "• **Entitlement**: 7 days per year (pro-rata basis)",
                    "• **Application**: Must be applied within 2 days if not applied earlier",
                    "• **Accumulation**: Cannot be carried forward to next year",
                    "• **Probation**: Available on pro-rata basis for probationary employees",
                    "• **Half Day**: Minimum 4 hours 15 minutes work required"
                ]
            },
            "privilege_leave": {
                "title": "🌴 **Privilege Leave (PL) Policy**",
                "details": [
                    "• **Entitlement**: 30 days per year (if 240 working days completed)",
                    "• **Minimum Duration**: 3 days minimum per application",
                    "• **Advance Notice**: 10 days advance application required",
                    "• **Carry Forward**: Up to 60 days can be carried forward",
                    "• **Encashment**: Excess over 60 days will be encashed",
                    "• **Credit Date**: January 7th every year"
                ]
            },
            "sick_leave": {
                "title": "🏥 **Sick Leave Policy**",
                "details": [
                    "• **Entitlement**: 7 days per year for confirmed employees",
                    "• **Medical Certificate**: Required for 3+ consecutive days",
                    "• **Pro-rata**: Based on confirmation/resignation timing",
                    "• **Extended Sickness**: Can use PL after SL exhaustion",
                    "• **Half Day**: Minimum 4 hours 15 minutes work required"
                ]
            },
            "maternity_leave": {
                "title": "👶 **Maternity Leave Policy**",
                "details": [
                    "• **Duration**: 26 weeks (182 days) for up to 2 deliveries",
                    "• **Pre-delivery**: Maximum 8 weeks before expected delivery",
                    "• **Adoption**: 12 weeks for commissioning/adopting mothers",
                    "• **Additional Children**: 12 weeks for more than 2 children",
                    "• **Medical Bonus**: INR 3,500 in absence of pre/post natal care",
                    "• **Miscarriage**: 6 weeks paid leave",
                    "• **Tubectomy**: 2 weeks paid leave",
                    "• **Nursing Breaks**: Until child attains 15 months",
                    "• **Crèche Facilities**: 4 visits per day allowed"
                ]
            }
        }

        # Determine which leave type to show based on intent
        if intent == "ask_casual_leave":
            response = f"{leave_info['casual_leave']['title']}\n\n" + "\n".join(leave_info['casual_leave']['details'])
        elif intent == "ask_privilege_leave":
            response = f"{leave_info['privilege_leave']['title']}\n\n" + "\n".join(leave_info['privilege_leave']['details'])
        elif intent == "ask_sick_leave":
            response = f"{leave_info['sick_leave']['title']}\n\n" + "\n".join(leave_info['sick_leave']['details'])
        elif intent == "ask_maternity_leave":
            response = f"{leave_info['maternity_leave']['title']}\n\n" + "\n".join(leave_info['maternity_leave']['details'])
        else:
            # General leave policy
            response = "📋 **Meril Group Leave Policies**\n\n"
            for leave_type, info in leave_info.items():
                response += f"{info['title']}\n" + "\n".join(info['details'][:3]) + "\n\n"
            response += "For detailed information on any specific leave type, please ask specifically!"

        if organization:
            response += f"\n\n*This policy applies to {organization}*"

        response += "\n\n**General Guidelines:**\n• All leaves (except sick leave) must be applied in advance\n• Leave applications require HOD approval\n• Weekly/Paid holidays during PL are counted as leave days"

        dispatcher.utter_message(text=response)
        return []


class ActionGetTravelPolicy(Action):
    def name(self) -> Text:
        return "action_get_travel_policy"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        organization = tracker.get_slot("organization")
        intent = tracker.latest_message['intent'].get('name')

        if intent == "ask_lta_policy":
            response = """🧳 **Leave Travel Allowance (LTA) Policy**

• **Eligibility**: After completion of 1 year of service
• **Frequency**: Calendar year basis (e.g., 2017 LTA claimed in 2018)
• **Leave Requirement**: Must avail 5 days privilege leave
• **Proportionate**: New joiners get proportionate LTA in subsequent year
• **Tax Benefits**: Subject to income tax regulations with travel proof
• **Accumulation**: Can be accumulated up to 3 years
• **Travel Proof**: Air tickets or rail tickets (2nd AC or 1st class)
• **Block Period**: Can be availed twice in 4-year block period"""
        else:
            response = """✈️ **Travel & Tour Policy**

**Domestic Travel:**
• Expense statements must be submitted within 10 days
• All bookings through Meril Travel Portal
• HOD approval required for reimbursement
• Tour report mandatory with expense claims
• Separate mention of credit card and cash payments

**International Travel:**
• Reimbursement on actual basis subject to policy limits
• Includes lodging, boarding, laundry, conveyance
• Hotel expenses include all service charges and taxes
• Airport tax and official calls reimbursed with bills

**General Guidelines:**
• All travel requests through Meril Travel Desk
• Complete guest expense details required
• Alcohol bills only for official guest entertainment
• Personal expenses not reimbursed
• 20+ hours outside qualifies as full day allowance"""

        if organization:
            response += f"\n\n*This policy applies to {organization}*"

        dispatcher.utter_message(text=response)
        return []


class ActionGetInsuranceInfo(Action):
    def name(self) -> Text:
        return "action_get_insurance_info"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        organization = tracker.get_slot("organization")

        response = """🛡️ **Term Life Insurance Policy**

**Coverage Details:**
• **Coverage**: Full life risk for all on-roll employees
• **Claim Amount**: Up to 7 times Annual CTC (Maximum Rs. 20 Lacs)
• **Additional Coverage**: General Manager and above get extra coverage
• **Geographic Scope**: Natural/accidental death within India borders
• **Family Coverage**: Only employees covered (not family members)

**Key Conditions:**
• **Medical Tests**: Employees above 60 years need medical examination
• **COVID Coverage**: Post-recovery coverage with negative RT-PCR test
• **Claim Payment**: To named beneficiary or nominee/legal heir
• **Employment Condition**: Coverage only while in employment

**Claim Process:**
• Contact HR department immediately
• Submit required documentation
• Family/nominee will receive compensation
• Processing as per insurance company guidelines

*This insurance policy demonstrates Meril's commitment to employee welfare and security.*"""

        if organization:
            response += f"\n\n*This policy applies to {organization}*"

        dispatcher.utter_message(text=response)
        return []


class ActionGetExpenseLimits(Action):
    def name(self) -> Text:
        return "action_get_expense_limits"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        organization = tracker.get_slot("organization")

        response = """💰 **Expense Limits & Reimbursement Policy**

**Mobile Phone Reimbursement:**
• **Assistant Manager**: Rs. 6,000/- (once in 3 years)
• **Manager/Sr. Manager/DGM**: Rs. 8,000/- (once in 3 years)
• **GM and Above**: Rs. 10,000/- (once in 3 years)
• **Monthly Reimbursement**: As per Corporate Plan

**Professional Attire (International Travel):**
• **Eligibility**: Employees with frequent international travel
• **Coverage**: Business Kit Allowance as defined
• **Approval**: HOD and Business Head recommendation + HR approval
• **Documentation**: Original bills required

**General Expense Guidelines:**
• All reimbursements subject to original bill submission
• Prior approval required for major expenses
• Expense claims within policy limits only
• Corporate plan rates apply for mobile reimbursements"""

        if organization:
            response += f"\n\n*This policy applies to {organization}*"

        dispatcher.utter_message(text=response)
        return []


class ActionGetHarassmentPolicy(Action):
    def name(self) -> Text:
        return "action_get_harassment_policy"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        organization = tracker.get_slot("organization")

        response = """🚫 **Sexual Harassment Prevention Policy**

**Definition of Sexual Harassment:**
Sexual harassment includes unwelcome sexually determined behavior such as:
• Physical contact and sexual advances
• Demand or request for sexual favors
• Sexually-colored remarks
• Showing pornography
• Any other unwelcome physical, verbal or non-verbal conduct of sexual nature

**Committee Members to Contact:**

**Meril Life Sciences Pvt. Ltd.:**
• **Presiding Officer**: Anita Nagar (<EMAIL>, **********)
• **Member**: Gayathri Nair, DGM (<EMAIL>, **********)
• **Member**: Ankita Patel, Manager (<EMAIL>, **********)
• **External Associate**: Neha Desai (<EMAIL>, **********)

**Meril Healthcare Pvt. Ltd.:**
• **Presiding Officer**: Pallabi Sarkar, DGM (<EMAIL>, **********)

**Meril Diagnostics Pvt. Ltd.:**
• **Presiding Officer**: Twisha Hathi, DGM (<EMAIL>, **********)

**Meril Endo Surgery Pvt. Ltd.:**
• **Presiding Officer**: Ami Rughani, DGM (<EMAIL>, **********)

**Redressal Process:**
• Send complaint in writing in sealed envelope to Head of Human Resource
• Investigation will be conducted and report submitted to Managing Director
• Counseling and appropriate measures will be taken
• Disciplinary action if grievance persists

*Committee validity: 17th Jan 2022 to 16th Jan 2025*"""

        if organization:
            response += f"\n\n*This policy applies to {organization}*"

        dispatcher.utter_message(text=response)
        return []


class ActionGetEthicalConduct(Action):
    def name(self) -> Text:
        return "action_get_ethical_conduct"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        organization = tracker.get_slot("organization")

        response = """⚖️ **Ethical Code of Conduct**

**Core Principles:**
• Adhere to rigorous ethical, professional and legal standards
• Treat co-workers with respect, courtesy, honesty and fairness
• Respect different values, beliefs, cultures and religions
• Work cooperatively and value contributions of others
• No bullying, intimidation, harassment or discrimination

**Key Areas:**

**Professionalism:**
• Commitment to professional standards in work and interactions
• Maintain highest standards of integrity and honesty
• Share information and assist in furthering company goals
• No misrepresentation of facts

**Conflict of Interest:**
• Avoid situations where personal interests conflict with company interests
• Act in company's interest at all times
• No use of company property/information for personal gain
• Disclose any potential conflicts immediately

**Confidentiality:**
• Protect confidential information about company, customers, suppliers
• Sign separate Confidentiality Agreement upon joining

**Company Property:**
• Protect and prevent theft/misuse of company assets
• Use equipment and resources only for legitimate business
• No unauthorized borrowing or disposal of company property

**Gifts & Benefits:**
• No gifts/entertainment that could influence business decisions
• Gifts over Rs. 1000 should be declined
• No bribes or inducements allowed

**Contact for Queries:**
• HR Office: <EMAIL>
• Report violations in good faith - whistleblower protection provided"""

        if organization:
            response += f"\n\n*This policy applies to {organization}*"

        dispatcher.utter_message(text=response)
        return []


class ActionProvideContactInfo(Action):
    def name(self) -> Text:
        return "action_provide_contact_info"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        response = """📞 **HR Contact Information**

**General HR Queries:**
• **Email**: <EMAIL>
• **Address**: Bilakhia House, Survey No.135/139, Muktanand Marg, Chala, Vapi-396191, Gujarat, India
• **Phone**: +91 ***********
• **Fax**: +91 **********5
• **Website**: www.merillife.com

**Sexual Harassment Committee Contacts:**
• **Meril Life Sciences**: Anita Nagar (<EMAIL>, **********)
• **Meril Healthcare**: Pallabi Sarkar (<EMAIL>, **********)
• **Meril Diagnostics**: Twisha Hathi (<EMAIL>, **********)
• **Meril Endo Surgery**: Ami Rughani (<EMAIL>, **********)

**Emergency Contacts:**
• For urgent HR matters, contact your immediate supervisor or HOD
• For policy clarifications, reach out to HR department
• For grievances, follow the escalation matrix as per company policy

*For specific department-related queries, please mention your organization and department for more targeted assistance.*"""

        dispatcher.utter_message(text=response)
        return []


class ActionRestart(Action):
    def name(self) -> Text:
        return "action_restart"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        dispatcher.utter_message(text="🔄 **Conversation Restarted**\n\nHello! I'm your HR Assistant for Meril Group Companies. I can help you with HR policies, leave information, travel policies, and more. Which organization are you from?")
        return [Restarted()]