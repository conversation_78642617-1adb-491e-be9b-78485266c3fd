from typing import Any, Text, Dict, List
from rasa_sdk import Action, Tracker
from rasa_sdk.executor import CollectingDispatcher
from rasa_sdk.events import SlotSet, Restarted

# Import knowledge base modules
from actions.knowledge_base.organization_data import (
    get_organization_key,
    get_sexual_harassment_committee,
    HR_CONTACTS
)
from actions.knowledge_base.department_data import (
    get_department_key,
    get_department_info,
    get_department_specific_policies
)
from actions.knowledge_base.policy_data import (
    LEAVE_POLICIES,
    TRAVEL_POLICIES,
    INSURANCE_POLICY,
    EXPENSE_LIMITS
)


class ActionGetLeavePolicy(Action):
    def name(self) -> Text:
        return "action_get_leave_policy"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        organization = tracker.get_slot("organization")
        department = tracker.get_slot("department")
        intent = tracker.latest_message['intent'].get('name')

        # Get organization and department specific information
        org_key = get_organization_key(organization) if organization else None
        dept_key = get_department_key(department) if department else None
        dept_info = get_department_info(department) if department else None

        # Determine which leave type to show based on intent
        if intent == "ask_casual_leave":
            policy = LEAVE_POLICIES["casual_leave"]
            response = f"{policy['title']}\n\n" + "\n".join(policy['details'])
        elif intent == "ask_privilege_leave":
            policy = LEAVE_POLICIES["privilege_leave"]
            response = f"{policy['title']}\n\n" + "\n".join(policy['details'])
        elif intent == "ask_sick_leave":
            policy = LEAVE_POLICIES["sick_leave"]
            response = f"{policy['title']}\n\n" + "\n".join(policy['details'])
        elif intent == "ask_maternity_leave":
            policy = LEAVE_POLICIES["maternity_leave"]
            response = f"{policy['title']}\n\n" + "\n".join(policy['details'])
        else:
            # General leave policy overview
            response = "📋 **Meril Group Leave Policies**\n\n"
            for leave_type, policy in LEAVE_POLICIES.items():
                response += f"{policy['title']}\n" + "\n".join(policy['details'][:3]) + "\n\n"
            response += "💡 *Ask specifically about any leave type for detailed information!*"

        # Add organization-specific information
        if organization:
            response += f"\n\n🏢 **Organization**: {organization}"

        # Add department-specific information
        if dept_info:
            response += f"\n🏛️ **Department**: {dept_info['full_name']}"

            # Add department-specific leave policies if any
            dept_policies = get_department_specific_policies(department)
            if dept_policies and "leave_approval" in dept_policies:
                response += f"\n📝 **Department Note**: {dept_policies['leave_approval']}"

        # Add general guidelines
        response += "\n\n**📋 General Guidelines:**"
        response += "\n• All leaves (except sick leave) must be applied in advance"
        response += "\n• Leave applications require HOD approval"
        response += "\n• Weekly/Paid holidays during PL are counted as leave days"
        response += "\n• Authority to approve leaves rests with Department Head and HR Department"

        # Add contact information for leave queries
        if dept_key == "hr":
            response += "\n\n💼 **HR Department**: You can directly process leave applications and queries"
        else:
            response += "\n\n📞 **For Leave Queries**: Contact your HOD or HR Department"

        dispatcher.utter_message(text=response)
        return []


class ActionGetTravelPolicy(Action):
    def name(self) -> Text:
        return "action_get_travel_policy"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        organization = tracker.get_slot("organization")
        intent = tracker.latest_message['intent'].get('name')

        if intent == "ask_lta_policy":
            response = """🧳 **Leave Travel Allowance (LTA) Policy**

• **Eligibility**: After completion of 1 year of service
• **Frequency**: Calendar year basis (e.g., 2017 LTA claimed in 2018)
• **Leave Requirement**: Must avail 5 days privilege leave
• **Proportionate**: New joiners get proportionate LTA in subsequent year
• **Tax Benefits**: Subject to income tax regulations with travel proof
• **Accumulation**: Can be accumulated up to 3 years
• **Travel Proof**: Air tickets or rail tickets (2nd AC or 1st class)
• **Block Period**: Can be availed twice in 4-year block period"""
        else:
            response = """✈️ **Travel & Tour Policy**

**Domestic Travel:**
• Expense statements must be submitted within 10 days
• All bookings through Meril Travel Portal
• HOD approval required for reimbursement
• Tour report mandatory with expense claims
• Separate mention of credit card and cash payments

**International Travel:**
• Reimbursement on actual basis subject to policy limits
• Includes lodging, boarding, laundry, conveyance
• Hotel expenses include all service charges and taxes
• Airport tax and official calls reimbursed with bills

**General Guidelines:**
• All travel requests through Meril Travel Desk
• Complete guest expense details required
• Alcohol bills only for official guest entertainment
• Personal expenses not reimbursed
• 20+ hours outside qualifies as full day allowance"""

        if organization:
            response += f"\n\n*This policy applies to {organization}*"

        dispatcher.utter_message(text=response)
        return []


class ActionGetInsuranceInfo(Action):
    def name(self) -> Text:
        return "action_get_insurance_info"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        organization = tracker.get_slot("organization")

        response = """🛡️ **Term Life Insurance Policy**

**Coverage Details:**
• **Coverage**: Full life risk for all on-roll employees
• **Claim Amount**: Up to 7 times Annual CTC (Maximum Rs. 20 Lacs)
• **Additional Coverage**: General Manager and above get extra coverage
• **Geographic Scope**: Natural/accidental death within India borders
• **Family Coverage**: Only employees covered (not family members)

**Key Conditions:**
• **Medical Tests**: Employees above 60 years need medical examination
• **COVID Coverage**: Post-recovery coverage with negative RT-PCR test
• **Claim Payment**: To named beneficiary or nominee/legal heir
• **Employment Condition**: Coverage only while in employment

**Claim Process:**
• Contact HR department immediately
• Submit required documentation
• Family/nominee will receive compensation
• Processing as per insurance company guidelines

*This insurance policy demonstrates Meril's commitment to employee welfare and security.*"""

        if organization:
            response += f"\n\n*This policy applies to {organization}*"

        dispatcher.utter_message(text=response)
        return []


class ActionGetExpenseLimits(Action):
    def name(self) -> Text:
        return "action_get_expense_limits"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        organization = tracker.get_slot("organization")

        response = """💰 **Expense Limits & Reimbursement Policy**

**Mobile Phone Reimbursement:**
• **Assistant Manager**: Rs. 6,000/- (once in 3 years)
• **Manager/Sr. Manager/DGM**: Rs. 8,000/- (once in 3 years)
• **GM and Above**: Rs. 10,000/- (once in 3 years)
• **Monthly Reimbursement**: As per Corporate Plan

**Professional Attire (International Travel):**
• **Eligibility**: Employees with frequent international travel
• **Coverage**: Business Kit Allowance as defined
• **Approval**: HOD and Business Head recommendation + HR approval
• **Documentation**: Original bills required

**General Expense Guidelines:**
• All reimbursements subject to original bill submission
• Prior approval required for major expenses
• Expense claims within policy limits only
• Corporate plan rates apply for mobile reimbursements"""

        if organization:
            response += f"\n\n*This policy applies to {organization}*"

        dispatcher.utter_message(text=response)
        return []


class ActionGetHarassmentPolicy(Action):
    def name(self) -> Text:
        return "action_get_harassment_policy"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        organization = tracker.get_slot("organization")
        department = tracker.get_slot("department")

        # Get organization-specific committee information
        committee_info = get_sexual_harassment_committee(organization) if organization else None

        response = """🚫 **Sexual Harassment Prevention Policy**

**Definition of Sexual Harassment:**
Sexual harassment includes unwelcome sexually determined behavior such as:
• Physical contact and sexual advances
• Demand or request for sexual favors
• Sexually-colored remarks
• Showing pornography
• Any other unwelcome physical, verbal or non-verbal conduct of sexual nature"""

        # Add organization-specific committee information
        if committee_info:
            response += f"\n\n**🏢 {committee_info['organization']} - Committee Members:**"
            for member in committee_info['committee']:
                response += f"\n• **{member['role']}**: {member['name']}"
                if 'designation' in member:
                    response += f", {member['designation']}"
                response += f" ({member['email']}, {member['phone']})"
            response += f"\n\n*Committee validity: {committee_info['validity']}*"
        else:
            # Show all committees if no specific organization
            response += "\n\n**Committee Members by Organization:**"

            # Meril Life Sciences
            response += "\n\n**🏢 Meril Life Sciences Pvt. Ltd.:**"
            response += "\n• **Presiding Officer**: Anita Nagar (<EMAIL>, **********)"
            response += "\n• **Member**: Gayathri Nair, DGM (<EMAIL>, **********)"
            response += "\n• **Member**: Ankita Patel, Manager (<EMAIL>, **********)"

            # Meril Healthcare
            response += "\n\n**🏢 Meril Healthcare Pvt. Ltd.:**"
            response += "\n• **Presiding Officer**: Pallabi Sarkar, DGM (<EMAIL>, **********)"

            # Meril Diagnostics
            response += "\n\n**🏢 Meril Diagnostics Pvt. Ltd.:**"
            response += "\n• **Presiding Officer**: Twisha Hathi, DGM (<EMAIL>, **********)"

            # Meril Endo Surgery
            response += "\n\n**🏢 Meril Endo Surgery Pvt. Ltd.:**"
            response += "\n• **Presiding Officer**: Ami Rughani, DGM (<EMAIL>, **********)"

            response += "\n\n*All committees valid: 17th Jan 2022 to 16th Jan 2025*"

        # Add redressal process
        response += "\n\n**📋 Redressal Process:**"
        response += "\n• Send complaint in writing in sealed envelope to Head of Human Resource"
        response += "\n• Investigation will be conducted and report submitted to Managing Director"
        response += "\n• Counseling and appropriate measures will be taken"
        response += "\n• Disciplinary action if grievance persists"

        # Add department-specific guidance
        if department:
            dept_info = get_department_info(department)
            if dept_info:
                response += f"\n\n🏛️ **Department**: {dept_info['full_name']}"
                if department.lower() == "hr":
                    response += "\n💼 **HR Note**: As HR department member, you can directly assist with harassment complaints and procedures"

        response += "\n\n⚠️ **Important**: All complaints are handled with strict confidentiality and zero tolerance for retaliation"

        dispatcher.utter_message(text=response)
        return []


class ActionGetEthicalConduct(Action):
    def name(self) -> Text:
        return "action_get_ethical_conduct"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        organization = tracker.get_slot("organization")

        response = """⚖️ **Ethical Code of Conduct**

**Core Principles:**
• Adhere to rigorous ethical, professional and legal standards
• Treat co-workers with respect, courtesy, honesty and fairness
• Respect different values, beliefs, cultures and religions
• Work cooperatively and value contributions of others
• No bullying, intimidation, harassment or discrimination

**Key Areas:**

**Professionalism:**
• Commitment to professional standards in work and interactions
• Maintain highest standards of integrity and honesty
• Share information and assist in furthering company goals
• No misrepresentation of facts

**Conflict of Interest:**
• Avoid situations where personal interests conflict with company interests
• Act in company's interest at all times
• No use of company property/information for personal gain
• Disclose any potential conflicts immediately

**Confidentiality:**
• Protect confidential information about company, customers, suppliers
• Sign separate Confidentiality Agreement upon joining

**Company Property:**
• Protect and prevent theft/misuse of company assets
• Use equipment and resources only for legitimate business
• No unauthorized borrowing or disposal of company property

**Gifts & Benefits:**
• No gifts/entertainment that could influence business decisions
• Gifts over Rs. 1000 should be declined
• No bribes or inducements allowed

**Contact for Queries:**
• HR Office: <EMAIL>
• Report violations in good faith - whistleblower protection provided"""

        if organization:
            response += f"\n\n*This policy applies to {organization}*"

        dispatcher.utter_message(text=response)
        return []


class ActionProvideContactInfo(Action):
    def name(self) -> Text:
        return "action_provide_contact_info"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        response = """📞 **HR Contact Information**

**General HR Queries:**
• **Email**: <EMAIL>
• **Address**: Bilakhia House, Survey No.135/139, Muktanand Marg, Chala, Vapi-396191, Gujarat, India
• **Phone**: +91 ***********
• **Fax**: +91 **********5
• **Website**: www.merillife.com

**Sexual Harassment Committee Contacts:**
• **Meril Life Sciences**: Anita Nagar (<EMAIL>, **********)
• **Meril Healthcare**: Pallabi Sarkar (<EMAIL>, **********)
• **Meril Diagnostics**: Twisha Hathi (<EMAIL>, **********)
• **Meril Endo Surgery**: Ami Rughani (<EMAIL>, **********)

**Emergency Contacts:**
• For urgent HR matters, contact your immediate supervisor or HOD
• For policy clarifications, reach out to HR department
• For grievances, follow the escalation matrix as per company policy

*For specific department-related queries, please mention your organization and department for more targeted assistance.*"""

        dispatcher.utter_message(text=response)
        return []


class ActionRestart(Action):
    def name(self) -> Text:
        return "action_restart"

    def run(self, dispatcher: CollectingDispatcher,
            tracker: Tracker,
            domain: Dict[Text, Any]) -> List[Dict[Text, Any]]:

        dispatcher.utter_message(text="🔄 **Conversation Restarted**\n\nHello! I'm your HR Assistant for Meril Group Companies. I can help you with HR policies, leave information, travel policies, and more. Which organization are you from?")
        return [Restarted()]