#!/usr/bin/env python3
"""
Demo script for HR Chatbot
This script demonstrates the chatbot functionality without requiring a full RASA setup
"""


def get_leave_policy_response(organization=None, intent_name="ask_leave_policy"):
    """Simulate leave policy response"""

    leave_info = {
        "casual_leave": {
            "title": "🏖️ **Casual Leave Policy**",
            "details": [
                "• **Entitlement**: 7 days per year (pro-rata basis)",
                "• **Application**: Must be applied within 2 days if not applied earlier",
                "• **Accumulation**: Cannot be carried forward to next year",
                "• **Probation**: Available on pro-rata basis for probationary employees",
                "• **Half Day**: Minimum 4 hours 15 minutes work required"
            ]
        },
        "privilege_leave": {
            "title": "🌴 **Privilege Leave (PL) Policy**",
            "details": [
                "• **Entitlement**: 30 days per year (if 240 working days completed)",
                "• **Minimum Duration**: 3 days minimum per application",
                "• **Advance Notice**: 10 days advance application required",
                "• **Carry Forward**: Up to 60 days can be carried forward",
                "• **Encashment**: Excess over 60 days will be encashed",
                "• **Credit Date**: January 7th every year"
            ]
        }
    }

    if intent_name == "ask_casual_leave":
        response = f"{leave_info['casual_leave']['title']}\n\n" + "\n".join(leave_info['casual_leave']['details'])
    elif intent_name == "ask_privilege_leave":
        response = f"{leave_info['privilege_leave']['title']}\n\n" + "\n".join(leave_info['privilege_leave']['details'])
    else:
        response = "📋 **Meril Group Leave Policies**\n\nI can help you with Casual Leave, Privilege Leave, Sick Leave, and Maternity Leave policies."

    if organization:
        response += f"\n\n*This policy applies to {organization}*"

    return response


def get_contact_info_response():
    """Simulate contact info response"""
    return """📞 **HR Contact Information**

**General HR Queries:**
• **Email**: <EMAIL>
• **Address**: Bilakhia House, Survey No.135/139, Muktanand Marg, Chala, Vapi-396191, Gujarat, India
• **Phone**: +91 ***********
• **Website**: www.merillife.com

**Sexual Harassment Committee Contacts:**
• **Meril Life Sciences**: Anita Nagar (<EMAIL>, **********)
• **Meril Healthcare**: Pallabi Sarkar (<EMAIL>, **********)
• **Meril Diagnostics**: Twisha Hathi (<EMAIL>, **********)
• **Meril Endo Surgery**: Ami Rughani (<EMAIL>, **********)"""


def demo_chatbot():
    print("🤖 HR Chatbot Demo for Meril Group Companies")
    print("=" * 50)

    # Demo scenarios
    scenarios = [
        {
            "title": "Casual Leave Policy Query",
            "organization": "Meril Life Sciences",
            "intent": "ask_casual_leave",
            "function": get_leave_policy_response
        },
        {
            "title": "Privilege Leave Policy Query",
            "organization": "Meril Healthcare",
            "intent": "ask_privilege_leave",
            "function": get_leave_policy_response
        },
        {
            "title": "Contact Information",
            "organization": None,
            "intent": "ask_contact_info",
            "function": get_contact_info_response
        }
    ]

    for i, scenario in enumerate(scenarios, 1):
        print(f"\n{i}. {scenario['title']}")
        print("-" * 30)

        # Get response based on scenario
        if scenario['function'] == get_contact_info_response:
            response = scenario['function']()
        else:
            response = scenario['function'](scenario['organization'], scenario['intent'])

        print(response)
        print("\n" + "="*50)

    print("\n🎯 **Interactive Demo**")
    print("Try asking questions like:")
    print("- What is the casual leave policy?")
    print("- Tell me about privilege leave")
    print("- What are the HR contact details?")
    print("\nType 'quit' to exit")

    while True:
        user_input = input("\n👤 You: ").strip().lower()

        if user_input in ['quit', 'exit', 'bye']:
            print("🤖 Bot: Goodbye! Have a great day!")
            break
        elif 'casual leave' in user_input:
            print("🤖 Bot:")
            print(get_leave_policy_response("Meril Life Sciences", "ask_casual_leave"))
        elif 'privilege leave' in user_input or 'pl' in user_input:
            print("🤖 Bot:")
            print(get_leave_policy_response("Meril Healthcare", "ask_privilege_leave"))
        elif 'contact' in user_input or 'hr' in user_input:
            print("🤖 Bot:")
            print(get_contact_info_response())
        else:
            print("🤖 Bot: I can help you with leave policies and contact information. Try asking about 'casual leave', 'privilege leave', or 'contact info'.")


if __name__ == "__main__":
    demo_chatbot()
