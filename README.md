# HR Chatbot for Meril Group Companies

An intelligent HR chatbot built with RASA framework to provide employees with instant access to HR policies, leave information, travel policies, and other HR-related queries for Meril Group Companies.

## Features

- **Leave Policies**: Casual, Privilege, Sick, and Maternity leave information
- **Travel Policies**: Domestic and International travel guidelines, LTA policy
- **Insurance Information**: Term life insurance coverage details
- **Expense Limits**: Mobile phone reimbursement and professional attire allowances
- **Sexual Harassment Policy**: Prevention guidelines and committee contact information
- **Ethical Conduct**: Code of conduct and professional behavior guidelines
- **Contact Information**: HR department and committee contact details

## Supported Organizations

- Meril Life Sciences Pvt. Ltd.
- Meril Healthcare Pvt. Ltd.
- Meril Diagnostics Pvt. Ltd.
- Meril Endo Surgery Pvt. Ltd.

## Project Structure

```
├── README.md
├── requirements.txt
├── config.yml                    # RASA pipeline configuration
├── domain.yml                    # Bot domain (intents, entities, responses)
├── credentials.yml               # Channel credentials
├── endpoints.yml                 # Custom action server endpoints
├── data/                         # Training data
│   ├── nlu.yml                   # NLU training examples
│   ├── stories.yml               # Conversation stories
│   ├── rules.yml                 # Conversation rules
│   └── lookup_tables/            # Lookup tables for entities
│       ├── organizations.txt
│       ├── departments.txt
│       └── hr_topics.txt
├── actions/                      # Custom actions
│   ├── __init__.py
│   └── actions.py                # Main custom actions
├── tests/                        # Test files
├── models/                       # Trained models
└── results/                      # Test results and evaluations
```

## Setup Instructions

### Prerequisites

- Python 3.9 (recommended for RASA compatibility)
- Virtual environment

### Installation

1. **Create and activate virtual environment:**
   ```bash
   python3.9 -m venv hr_chatbot_env
   source hr_chatbot_env/bin/activate  # On Windows: hr_chatbot_env\Scripts\activate
   ```

2. **Install dependencies:**
   ```bash
   pip install --upgrade pip
   pip install rasa==3.6.13
   pip install rasa-sdk==3.6.2
   ```

3. **Train the model:**
   ```bash
   rasa train
   ```

4. **Start the action server (in a separate terminal):**
   ```bash
   source hr_chatbot_env/bin/activate
   rasa run actions
   ```

5. **Start the RASA server:**
   ```bash
   rasa run --enable-api --cors "*"
   ```

6. **Test the chatbot:**
   ```bash
   rasa shell
   ```

## Usage Examples

### Sample Conversations

**Leave Policy Query:**
```
User: Hi
Bot: Hello! I'm your HR Assistant for Meril Group Companies...
User: I work at Meril Life Sciences
User: What is the casual leave policy?
Bot: [Provides detailed casual leave information]
```

**Travel Policy Query:**
```
User: Tell me about LTA policy
Bot: [Provides Leave Travel Allowance details]
```

**Sexual Harassment Policy:**
```
User: How to report sexual harassment?
Bot: [Provides committee contact information and process]
```

## Testing

Run NLU tests:
```bash
rasa test nlu
```

Run story tests:
```bash
rasa test
```

## Deployment

For production deployment, consider:
- Setting up proper database for conversation tracking
- Implementing authentication for employee verification
- Integrating with existing HR systems
- Setting up monitoring and analytics

## Data Sources

The chatbot is trained on official HR documents from Meril Group:
- HR Manual 2023
- Ethical Code of Conduct
- Sexual Harassment Prevention Notices
- Leave and Travel Policies

## Contributing

1. Add new training examples to `data/nlu.yml`
2. Create new stories in `data/stories.yml`
3. Implement custom actions in `actions/actions.py`
4. Update domain.yml with new intents/entities
5. Retrain the model: `rasa train`

## Support

For technical support or questions about the chatbot, contact the development team or HR <NAME_EMAIL>.
