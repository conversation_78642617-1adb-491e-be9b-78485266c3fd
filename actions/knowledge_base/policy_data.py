"""
Comprehensive policy data for Meril Group Companies
Based on official HR documents and manuals
"""

# Leave Policies - Applicable to all Meril Group Companies
LEAVE_POLICIES = {
    "casual_leave": {
        "title": "🏖️ Casual Leave Policy",
        "entitlement": "7 days per year",
        "basis": "Pro-rata basis",
        "accumulation": "Cannot be carried forward to next year",
        "probation": "Available on pro-rata basis for probationary employees",
        "half_day": "Minimum 4 hours 15 minutes work required",
        "application": "Must be applied within 2 days if not applied earlier",
        "details": [
            "• **Entitlement**: 7 days per year (pro-rata basis)",
            "• **Credit**: First day of calendar year",
            "• **Accumulation**: Cannot be carried forward beyond current year",
            "• **Probation**: Available on pro-rata basis for probationary employees",
            "• **Half Day**: Minimum 4 hours 15 minutes work required",
            "• **Application**: Must be filled within 2 days of availing leave"
        ]
    },
    "privilege_leave": {
        "title": "🌴 Privilege Leave (PL) Policy",
        "entitlement": "30 days per year",
        "eligibility": "If 240 working days completed in calendar year",
        "minimum_duration": "3 days minimum per application",
        "advance_notice": "10 days advance application required",
        "carry_forward": "Up to 60 days can be carried forward",
        "encashment": "Excess over 60 days will be encashed",
        "credit_date": "January 7th every year",
        "details": [
            "• **Entitlement**: 30 days per year (if 240 working days completed)",
            "• **Minimum Duration**: 3 days minimum per application",
            "• **Advance Notice**: 10 days advance application required",
            "• **Carry Forward**: Up to 60 days can be carried forward",
            "• **Encashment**: Excess over 60 days will be encashed",
            "• **Credit Date**: January 7th every year",
            "• **Emergency**: Short notice PL on emergency grounds (management discretion)",
            "• **Extension**: Application for extension must reach before expiry",
            "• **Advance PL**: Available for marriage (self) or sickness after exhausting other leaves"
        ]
    },
    "sick_leave": {
        "title": "🏥 Sick Leave Policy",
        "entitlement": "7 days per year",
        "eligibility": "Confirmed employees (pro-rata for others)",
        "medical_certificate": "Required for 3+ consecutive days",
        "extended_sickness": "Can use PL after SL exhaustion with medical certificate",
        "details": [
            "• **Entitlement**: 7 days per year for confirmed employees",
            "• **Pro-rata**: Based on confirmation/resignation timing",
            "• **Medical Certificate**: Required for 3+ consecutive days",
            "• **Notification**: Inform company during leave period with medical certificate",
            "• **Extended Sickness**: Can use PL after SL exhaustion",
            "• **Half Day**: Minimum 4 hours 15 minutes work required"
        ]
    },
    "maternity_leave": {
        "title": "👶 Maternity Leave Policy",
        "duration": "26 weeks (182 days)",
        "pre_delivery": "Maximum 8 weeks before expected delivery",
        "max_deliveries": "For maximum of two deliveries",
        "adoption": "12 weeks for commissioning/adopting mothers",
        "additional_children": "12 weeks for more than 2 children",
        "details": [
            "• **Duration**: 26 weeks (182 days) for up to 2 deliveries",
            "• **Pre-delivery**: Maximum 8 weeks before expected delivery",
            "• **Adoption**: 12 weeks for commissioning/adopting mothers",
            "• **Additional Children**: 12 weeks for more than 2 children",
            "• **Work from Home**: Option available on agreed terms",
            "• **Medical Bonus**: INR 3,500 in absence of pre/post natal care",
            "• **Miscarriage**: 6 weeks paid leave",
            "• **Tubectomy**: 2 weeks paid leave",
            "• **Nursing Breaks**: Until child attains 15 months",
            "• **Crèche Facilities**: 4 visits per day allowed",
            "• **ESIC**: Follow ESIC procedure if covered"
        ]
    }
}

# Travel Policies
TRAVEL_POLICIES = {
    "domestic": {
        "title": "✈️ Domestic Travel Policy",
        "scope": "All employees of Meril Life Sciences Pvt. Ltd.",
        "expense_submission": "Within 10 days after tour completion",
        "approval": "HOD approval required",
        "booking": "Through Meril Travel Portal and Travel Desk",
        "details": [
            "• **Scope**: All employees for domestic travel",
            "• **Expense Submission**: Within 10 days after tour completion",
            "• **Approval**: HOD approval required for reimbursement",
            "• **Booking**: All requests through Meril Travel Portal",
            "• **Tour Report**: Mandatory with expense claims",
            "• **Payment Details**: Separate mention of credit card and cash",
            "• **Guest Expenses**: Complete details required",
            "• **Alcohol**: Only for official guest entertainment"
        ]
    },
    "international": {
        "title": "🌍 International Travel Policy",
        "scope": "All employees for international travel",
        "reimbursement": "On actual basis subject to policy limits",
        "inclusions": "Lodging, boarding, laundry, conveyance",
        "day_calculation": "Airport to airport including travel time",
        "details": [
            "• **Reimbursement**: On actual basis subject to policy limits",
            "• **Inclusions**: Lodging, boarding, laundry, conveyance",
            "• **Hotel Expenses**: All service charges and taxes included",
            "• **Day Calculation**: Airport to airport including travel time",
            "• **Partial Day**: 20+ hours outside qualifies as full day",
            "• **Personal Expenses**: Not reimbursed",
            "• **Official Expenses**: Airport tax, calls, fax with bills"
        ]
    },
    "lta": {
        "title": "🧳 Leave Travel Allowance (LTA)",
        "eligibility": "After 1 year of service",
        "basis": "Calendar year basis",
        "leave_requirement": "5 days privilege leave required",
        "accumulation": "Up to 3 years",
        "tax_benefits": "Subject to income tax regulations with travel proof",
        "details": [
            "• **Eligibility**: After completion of 1 year of service",
            "• **Basis**: Calendar year basis (e.g., 2017 LTA claimed in 2018)",
            "• **Leave Requirement**: Must avail 5 days privilege leave",
            "• **Proportionate**: New joiners get proportionate LTA",
            "• **Tax Benefits**: With travel proof (air/rail tickets 2nd AC or 1st class)",
            "• **Accumulation**: Can be accumulated up to 3 years",
            "• **Block Period**: Twice in 4-year block period"
        ]
    }
}

# Insurance Policy
INSURANCE_POLICY = {
    "title": "🛡️ Term Life Insurance Policy",
    "coverage": "Full life risk for all on-roll employees",
    "claim_amount": "Up to 7 times Annual CTC (Maximum Rs. 20 Lacs)",
    "additional_coverage": "General Manager and above get extra coverage",
    "geographic_scope": "Natural/accidental death within India borders",
    "family_coverage": "Only employees covered (not family members)",
    "details": [
        "• **Coverage**: Full life risk for all on-roll employees",
        "• **Claim Amount**: Up to 7 times Annual CTC (Maximum Rs. 20 Lacs)",
        "• **Additional Coverage**: General Manager and above get extra coverage",
        "• **Geographic Scope**: Natural/accidental death within India borders",
        "• **Family Coverage**: Only employees covered (not family members)",
        "• **Medical Tests**: Employees above 60 years need medical examination",
        "• **COVID Coverage**: Post-recovery coverage with negative RT-PCR test",
        "• **Claim Payment**: To named beneficiary or nominee/legal heir",
        "• **Employment Condition**: Coverage only while in employment"
    ]
}

# Expense Limits
EXPENSE_LIMITS = {
    "mobile_phone": {
        "title": "📱 Mobile Phone Reimbursement",
        "frequency": "Once in 3 years",
        "limits": {
            "assistant_manager": "Rs. 6,000/-",
            "manager_sr_manager_dgm": "Rs. 8,000/-",
            "gm_and_above": "Rs. 10,000/-"
        },
        "monthly": "As per Corporate Plan",
        "details": [
            "• **Assistant Manager**: Rs. 6,000/- (once in 3 years)",
            "• **Manager/Sr. Manager/DGM**: Rs. 8,000/- (once in 3 years)",
            "• **GM and Above**: Rs. 10,000/- (once in 3 years)",
            "• **Monthly Reimbursement**: As per Corporate Plan negotiated for Group"
        ]
    },
    "professional_attire": {
        "title": "👔 Professional Attire (International Travel)",
        "eligibility": "Employees with frequent international travel",
        "coverage": "Business Kit Allowance as defined",
        "approval": "HOD and Business Head recommendation + HR approval",
        "documentation": "Original bills required",
        "details": [
            "• **Eligibility**: Employees with frequent international travel",
            "• **Coverage**: Business Kit Allowance as defined",
            "• **Approval**: HOD and Business Head recommendation + HR approval",
            "• **Documentation**: Original bills required",
            "• **Process**: Purchase bills with proper recommendations"
        ]
    }
}
