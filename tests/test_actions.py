import pytest
from rasa_sdk import Tracker
from rasa_sdk.executor import CollectingDispatcher
from actions.actions import (
    ActionGetLeavePolicy,
    ActionGetTravelPolicy,
    ActionGetInsuranceInfo,
    ActionGetExpenseLimits,
    ActionGetHarassmentPolicy,
    ActionGetEthicalConduct,
    ActionProvideContactInfo,
    ActionRestart
)


class TestHRActions:
    
    def test_action_get_leave_policy(self):
        action = ActionGetLeavePolicy()
        dispatcher = CollectingDispatcher()
        tracker = Tracker(
            sender_id="test_user",
            slots={"organization": "Meril Life Sciences"},
            latest_message={"intent": {"name": "ask_leave_policy"}},
            events=[],
            paused=False,
            followup_action=None,
            active_loop=None,
            latest_action_name=None
        )
        
        result = action.run(dispatcher, tracker, {})
        assert result == []
        assert len(dispatcher.messages) > 0
    
    def test_action_get_travel_policy(self):
        action = ActionGetTravelPolicy()
        dispatcher = CollectingDispatcher()
        tracker = Tracker(
            sender_id="test_user",
            slots={"organization": "Meril Healthcare"},
            latest_message={"intent": {"name": "ask_travel_policy"}},
            events=[],
            paused=False,
            followup_action=None,
            active_loop=None,
            latest_action_name=None
        )
        
        result = action.run(dispatcher, tracker, {})
        assert result == []
        assert len(dispatcher.messages) > 0
    
    def test_action_get_insurance_info(self):
        action = ActionGetInsuranceInfo()
        dispatcher = CollectingDispatcher()
        tracker = Tracker(
            sender_id="test_user",
            slots={"organization": "Meril Diagnostics"},
            latest_message={"intent": {"name": "ask_insurance_policy"}},
            events=[],
            paused=False,
            followup_action=None,
            active_loop=None,
            latest_action_name=None
        )
        
        result = action.run(dispatcher, tracker, {})
        assert result == []
        assert len(dispatcher.messages) > 0
    
    def test_action_provide_contact_info(self):
        action = ActionProvideContactInfo()
        dispatcher = CollectingDispatcher()
        tracker = Tracker(
            sender_id="test_user",
            slots={},
            latest_message={"intent": {"name": "ask_contact_info"}},
            events=[],
            paused=False,
            followup_action=None,
            active_loop=None,
            latest_action_name=None
        )
        
        result = action.run(dispatcher, tracker, {})
        assert result == []
        assert len(dispatcher.messages) > 0
        assert "<EMAIL>" in dispatcher.messages[0]["text"]


if __name__ == "__main__":
    pytest.main([__file__])
