#!/bin/bash

# HR Chatbot Setup Script for Meril Group Companies

echo "🤖 Setting up HR Chatbot for Meril Group Companies..."

# Check if Python 3.9 is available
if ! command -v python3.9 &> /dev/null; then
    echo "❌ Python 3.9 not found. Installing..."
    sudo apt update
    sudo apt install -y python3.9 python3.9-venv python3.9-dev
fi

# Create virtual environment
echo "📦 Creating virtual environment..."
python3.9 -m venv hr_chatbot_env

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source hr_chatbot_env/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install RASA and dependencies
echo "📥 Installing RASA and dependencies..."
pip install rasa==3.6.13
pip install rasa-sdk==3.6.2

# Install additional dependencies
echo "📥 Installing additional dependencies..."
pip install pytest
pip install requests

# Train the model
echo "🎯 Training the RASA model..."
rasa train

echo "✅ Setup complete!"
echo ""
echo "🚀 To start the chatbot:"
echo "1. Start action server: rasa run actions"
echo "2. In another terminal, start RASA: rasa run --enable-api --cors '*'"
echo "3. Test with: rasa shell"
echo ""
echo "📚 For more information, see README.md"
